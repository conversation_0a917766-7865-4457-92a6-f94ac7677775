<?php
// Debug script to trace the finish hit workflow

declare(strict_types=1);

global $conexion;

use App\classes\Hit;

require_once 'config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Enable error logging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __ROOT__ . '/debug_finish_hit.log');

echo "<h1>Debug Finish Hit Workflow</h1>";

try {
    // First, let's create a test hit
    echo "<h2>Step 1: Creating a test hit</h2>";
    
    setTimeZoneCol();
    
    $testHit = new Hit();
    $testHit->setDescripcion("Test Hit for Debug");
    $testHit->setRequester("Debug User");
    $testHit->setPago(10.50);
    $testHit->setNota("This is a test hit for debugging");
    $testHit->setFecha(create_datetime());
    
    $result = $testHit->guardar($conexion);
    
    if ($result) {
        $testHitId = $testHit->getId();
        echo "✓ Test hit created successfully with ID: " . $testHitId . "<br>";
        
        // Now let's test the finish hit functionality
        echo "<h2>Step 2: Testing finish hit functionality</h2>";
        
        // Count hits before finishing
        $hitsBefore = Hit::getTodaysList($conexion);
        $countBefore = count($hitsBefore);
        echo "Hits count before finishing: " . $countBefore . "<br>";
        
        // Finish the hit
        echo "Finishing hit with ID: " . $testHitId . "<br>";
        $finishResult = Hit::markAsFinished($testHitId, $conexion);
        
        if ($finishResult) {
            echo "✓ Hit marked as finished successfully<br>";
            
            // Count hits after finishing
            $hitsAfter = Hit::getTodaysList($conexion);
            $countAfter = count($hitsAfter);
            echo "Hits count after finishing: " . $countAfter . "<br>";
            
            if ($countAfter > $countBefore) {
                echo "❌ PROBLEM DETECTED: Hit count increased after finishing! This suggests duplication.<br>";
                
                // Let's examine the hits to see what happened
                echo "<h3>Examining hits after finish operation:</h3>";
                foreach ($hitsAfter as $hit) {
                    echo "Hit ID: " . $hit->getId() . 
                         " | Descripcion: " . $hit->getDescripcion() . 
                         " | Terminado: " . $hit->getTerminado() . 
                         " | Fecha: " . $hit->getFecha() . "<br>";
                }
            } else {
                echo "✓ Hit count remained the same. No duplication detected in direct test.<br>";
            }
            
            // Let's also check the specific hit we finished
            $finishedHit = Hit::get($testHitId, $conexion);
            if ($finishedHit) {
                echo "Finished hit status - Terminado: " . $finishedHit->getTerminado() . 
                     " | Fecha Terminado: " . $finishedHit->getFechaTerminado() . "<br>";
            }
            
        } else {
            echo "❌ Failed to finish hit<br>";
        }
        
    } else {
        echo "❌ Failed to create test hit<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    error_log("Debug script error: " . $e->getMessage());
}

echo "<h2>Step 3: Simulating AJAX finish hit call</h2>";

// Simulate the exact AJAX call that happens in the real application
try {
    // Create another test hit
    $testHit2 = new Hit();
    $testHit2->setDescripcion("Test Hit 2 for AJAX Debug");
    $testHit2->setRequester("AJAX Debug User");
    $testHit2->setPago(15.75);
    $testHit2->setNota("This is test hit 2 for AJAX debugging");
    $testHit2->setFecha(create_datetime());
    
    $result2 = $testHit2->guardar($conexion);
    
    if ($result2) {
        $testHit2Id = $testHit2->getId();
        echo "✓ Test hit 2 created successfully with ID: " . $testHit2Id . "<br>";
        
        // Count hits before AJAX call
        $hitsBefore2 = Hit::getTodaysList($conexion);
        $countBefore2 = count($hitsBefore2);
        echo "Hits count before AJAX finish: " . $countBefore2 . "<br>";
        
        // Simulate the exact AJAX call
        $_POST['ajax_finish_hit'] = true;
        $_POST['hit_id'] = $testHit2Id;
        
        // Capture output
        ob_start();
        
        // Execute the same code as in lhits.php AJAX handler
        if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_finish_hit'])) {
            header('Content-Type: application/json');
            try {
                setTimeZoneCol();
                $hitId = limpiar_datos($_POST['hit_id']);
                Hit::markAsFinished($hitId, $conexion);
                echo json_encode(['success' => true, 'message' => 'Hit marcado como terminado']);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => $e->getMessage()]);
            }
        }
        
        $ajaxOutput = ob_get_clean();
        echo "AJAX response: " . $ajaxOutput . "<br>";
        
        // Count hits after AJAX call
        $hitsAfter2 = Hit::getTodaysList($conexion);
        $countAfter2 = count($hitsAfter2);
        echo "Hits count after AJAX finish: " . $countAfter2 . "<br>";
        
        if ($countAfter2 > $countBefore2) {
            echo "❌ PROBLEM DETECTED: Hit count increased after AJAX finish! This suggests duplication in AJAX handler.<br>";
        } else {
            echo "✓ AJAX finish worked correctly. No duplication detected.<br>";
        }
        
        // Clean up POST data
        unset($_POST['ajax_finish_hit']);
        unset($_POST['hit_id']);
    }
    
} catch (Exception $e) {
    echo "❌ AJAX simulation error: " . $e->getMessage() . "<br>";
}

echo "<h2>Debug Complete</h2>";
echo "<p>Check the debug_finish_hit.log file for detailed error logs.</p>";
?>