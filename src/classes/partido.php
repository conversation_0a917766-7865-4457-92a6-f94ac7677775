<?php

require_once __ROOT__ . '/src/classes/apuestatipo.php';
require_once __ROOT__ . '/src/classes/partidocorner.php';
require_once __ROOT__ . '/src/classes/partidoapuesta.php';
require_once __ROOT__ . '/src/classes/pais.php';
require_once __ROOT__ . '/src/classes/partidoinfo.php';

class Partido
{
	public string  $id;
	public string  $home;
	public string  $away;
	public string  $pais;
	public Pais    $pais_torneo;
	public string  $diasemana;
	public ?string $fecha;
	public ?string $fechadiashora;
	public string  $hora;
	public ?string $horamilitar;
	public int     $num_week;
	public float   $formhome;
	public float   $formaway;
	public int     $odds_reviewed;
	public float   $home_xg;
	public float   $away_xg;
	public int     $revisado_probabilidades;
	public int     $estado;
	
	public string  $horastring;
	public array   $apuestas;
	public int     $num_apuestas_realizadas;
	public int     $num_apuestas_resueltas;
	public int     $num_apuestas_pendientes;
	public array   $reporte_profitsemanal;
	public array   $reporte_winratioporpais;
	public string  $bgcolor;
	public int     $num_corners;
	public string  $bgcolorformhome;
	public string  $bgcolorformaway;
	public string  $seasons;
	public int     $numero_partidos;
	public ?string $max_fecha_partido_info;
	
	public string $bd_table                   = 'partidos';
	public string $bd_alias                   = 'part';
	public string $bd_id                      = 'id_partido';
	public string $bd_home                    = 'home';
	public string $bd_away                    = 'away';
	public string $bd_pais                    = 'pais';
	public string $bd_id_pais                 = 'id_pais';
	public string $bd_nombre_pais             = 'nombre_pais';
	public string $bd_diasemana               = 'dia_semana';
	public string $bd_fecha                   = 'fecha';
	public string $bd_hora                    = 'hora';
	public string $bd_horamilitar             = 'hora_militar';
	public string $bd_num_week                = 'num_week';
	public string $bd_formhome                = 'form_home';
	public string $bd_formaway                = 'form_away';
	public string $bd_odds_reviewed           = 'odds_reviewed';
	public string $bd_home_xg                 = 'home_xg';
	public string $bd_away_xg                 = 'away_xg';
	public string $bd_revisado_probabilidades = 'revisado_probabilidades';
	public string $bd_estado                  = 'estado';
	
	public string $bd_g_numero_partidos        = 'numero_partidos';
	public string $bd_g_max_fecha_partido_info = 'max_fecha_partido_info';
	
	function __construct()
	{
		$this->id                      = '';
		$this->home                    = '';
		$this->away                    = '';
		$this->pais                    = '';
		$this->pais_torneo             = new Pais();
		$this->diasemana               = '';
		$this->fecha                   = '';
		$this->fechadiashora           = '';
		$this->hora                    = '';
		$this->horastring              = '';
		$this->horamilitar             = 0;
		$this->num_week                = 0;
		$this->formhome                = 0;
		$this->formaway                = 0;
		$this->odds_reviewed           = 0;
		$this->home_xg                 = 0;
		$this->away_xg                 = 0;
		$this->revisado_probabilidades = 0;
		$this->estado                  = 0;
		
		$this->apuestas                = array();
		$this->num_apuestas_realizadas = 0;
		$this->num_apuestas_resueltas  = 0;
		$this->num_apuestas_pendientes = 0;
		$this->reporte_profitsemanal   = array();
		$this->reporte_winratioporpais = array();
		$this->bgcolor                 = '';
		$this->num_corners             = 0;
		$this->bgcolorformhome         = '';
		$this->bgcolorformaway         = '';
		$this->seasons                 = '';
		$this->numero_partidos         = 0;
		$this->max_fecha_partido_info  = '';
	}
	
	/**
	 * @param $resultado
	 *
	 * @return self
	 * @throws Exception
	 */
	public static function construct($resultado, $conexion): self
	{
		try {
			$cq = new self;
			
			$objeto                      = new self;
			$objeto->id                  = desordena($resultado[$cq->bd_id]);
			$objeto->home                = $resultado[$cq->bd_home];
			$objeto->away                = $resultado[$cq->bd_away];
			$objeto->pais                = $resultado[$cq->bd_pais];
			$objeto->pais_torneo->id     = desordena($resultado[$cq->bd_id_pais]);
			$objeto->pais_torneo->nombre = (isset($resultado["nombre_pais"])) ? $resultado["nombre_pais"] : "";
			
			$idpais          = Pais::getByNombre($objeto->pais, $conexion);
			$objeto->seasons = PartidoInfo::get_seasons_byidpais($idpais, $conexion);
			
			$objeto->diasemana   = $resultado[$cq->bd_diasemana];
			$objeto->fecha       = $resultado[$cq->bd_fecha];
			$objeto->hora        = $resultado[$cq->bd_hora];
			$objeto->horamilitar = $resultado[$cq->bd_horamilitar];
			
			if (!empty($objeto->horamilitar)) {
				$objeto->horastring = convertHoraMilitarAHoraMinutoString($objeto->hora, $objeto->horamilitar);
			} else {
				$objeto->horastring = convertHoraMilitarAHoraString($resultado[$cq->bd_hora]);
			}
			
			$objeto->fechadiashora           = self::formatFechaDiasHora($objeto->fecha, $objeto->horastring);
			$objeto->num_week                = $resultado[$cq->bd_num_week];
			$objeto->formhome                = $resultado[$cq->bd_formhome];
			$objeto->formaway                = $resultado[$cq->bd_formaway];
			$objeto->odds_reviewed           = $resultado[$cq->bd_odds_reviewed];
			$objeto->home_xg                 = $resultado[$cq->bd_home_xg];
			$objeto->away_xg                 = $resultado[$cq->bd_away_xg];
			$objeto->revisado_probabilidades = $resultado[$cq->bd_revisado_probabilidades];
			$objeto->estado                  = $resultado[$cq->bd_estado];
			$objeto->num_apuestas_pendientes = PartidoApuesta::getNumApuestasPendientes($objeto->id, $conexion);
			$objeto->num_apuestas_realizadas = PartidoApuesta::getNumApuestasRealizadas($objeto->id, $conexion);
			$objeto->numero_partidos         = (isset($resultado["$cq->bd_g_numero_partidos"])) ? $resultado["$cq->bd_g_numero_partidos"] : 0;
			
			if ($objeto->num_apuestas_realizadas == 0) {
				$objeto->bgcolor = 'bg-gray';
				
			} else {
				if ($objeto->num_apuestas_pendientes > 0) {
					$objeto->bgcolor = COLOR_WARNING;
					
				} else {
					$objeto->bgcolor = COLOR_INFO;
				}
			}
			
			$objeto->num_corners            = PartidoCorner::getNumCorners($objeto->id, $conexion);
			$objeto->bgcolorformhome        = self::definirBgColorForm($objeto->formhome);
			$objeto->bgcolorformaway        = self::definirBgColorForm($objeto->formaway);
			$objeto->max_fecha_partido_info = (isset($resultado["$cq->bd_g_max_fecha_partido_info"])) ? $resultado["$cq->bd_g_max_fecha_partido_info"] : "";
			
			return $objeto;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get($id, $conexion): self
	{
		try {
			$query = <<<SQL
					SELECT
						 p.*
						,pa.nombre AS nombre_pais
					FROM partidos p
						INNER JOIN paises pa ON pa.id_pais = p.id_pais
					WHERE p.id_partido = :id_partido
					SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":id_partido", ordena($id));
			$statement->execute();
			$resultado = $statement->fetch();
			
			if ($resultado) {
				return self::construct($resultado, $conexion);
				
			} else {
				return new self;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function getExiste($partido, $conexion): int
	{
		try {
			$cq  = new self;
			$cqa = $cq->bd_alias;
			
			$query = "SELECT ";
			$query .= "  $cqa.* ";
			$query .= "FROM $cq->bd_table $cqa ";
			$query .= "WHERE ";
			$query .= "  $cqa.$cq->bd_home = :$cq->bd_home ";
			$query .= "  AND $cqa.$cq->bd_away = :$cq->bd_away ";
			$query .= "  AND $cqa.$cq->bd_fecha = :$cq->bd_fecha ";
			$query .= "  AND $cqa.$cq->bd_estado = :$cq->bd_estado ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_estado", 1);
			$statement->bindValue(":$cq->bd_home", $partido->home);
			$statement->bindValue(":$cq->bd_away", $partido->away);
			$statement->bindValue(":$cq->bd_fecha", $partido->fecha);
			$statement->execute();
			$resultado = $statement->fetch();
			
			if ($resultado) {
				return 1;
				
			} else {
				return 0;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_count_odds_por_revisar(PDO $conexion): int
	{
		try {
			$param                          = array();
			$param['solo_odds_por_revisar'] = 1;
			
			return count(self::getList($param, $conexion));
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	private static function get_query_por_revisar_probabilidades(): string
	{
		try {
			return <<<SQL
					SELECT
						p.id_partido
					FROM partidos p
					WHERE
						p.estado = 1
						AND p.revisado_probabilidades = 0
						AND CONCAT(p.fecha, ' ', p.hora_militar) >= :fechahora
					ORDER BY
						 p.fecha
						,p.hora
						,p.id_partido
					SQL;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_next_por_revisar_probabilidades($conexion): string
	{
		try {
			$fechahora = create_date() . ' ' . createDateMilitaryHour();
			
			$query = self::get_query_por_revisar_probabilidades();
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":fechahora", $fechahora);
			$statement->execute();
			$resultado = $statement->fetch();
			
			if ($resultado) {
				return desordena($resultado["id_partido"]);
				
			} else {
				return '';
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_numero_por_revisar_probabilidades($paramref, $conexion): int
	{
		try {
			
			$fechahora = create_date() . ' ' . createDateMilitaryHour();
			
			$query = self::get_query_por_revisar_probabilidades();
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":fechahora", $fechahora);
			$statement->execute();
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				return 0;
				
			} else {
				return count($resultados);
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function getList($paramref, $conexion): array
	{
		try {
			#region region parametros
			$matchup               = (isset($paramref['matchup'])) ? $paramref['matchup'] : '';
			$fecha                 = (isset($paramref['fecha'])) ? $paramref['fecha'] : '';
			$torneo                = (isset($paramref['torneo'])) ? $paramref['torneo'] : '';
			$solo_odds_por_revisar = (isset($paramref['solo_odds_por_revisar'])) ? $paramref['solo_odds_por_revisar'] : 0;
			$solo_por_jugar        = (isset($paramref['solo_por_jugar'])) ? $paramref['solo_por_jugar'] : 0;
			$groupby_torneo        = (isset($paramref['groupby_torneo'])) ? $paramref['groupby_torneo'] : 0;
			#endregion parametros
			
			$cur_hora = create_hour_24_format();
			
			$cq          = new self;
			$cqa         = $cq->bd_alias;
			$cq_parinf   = new PartidoInfo();
			$cq_parinf_a = $cq_parinf->bd_alias;
			
			$query = "SELECT ";
			$query .= "  $cqa.* ";
			
			if ($groupby_torneo == 1) {
				$query .= ",IFNULL(MAX($cq_parinf_a.$cq_parinf->bd_fecha), '') $cq->bd_g_max_fecha_partido_info  ";
				$query .= ",COUNT(DISTINCT $cqa.$cq->bd_id) $cq->bd_g_numero_partidos ";
			}
			
			$query .= "FROM $cq->bd_table $cq->bd_alias ";
			
			if ($groupby_torneo == 1) {
				$query .= "LEFT JOIN $cq_parinf->bd_table $cq_parinf_a ";
				$query .= "  ON ($cq_parinf_a.$cq_parinf->bd_idpais = $cqa.$cq->bd_id_pais AND $cq_parinf_a.$cq_parinf->bd_status = 'COMPLETE' AND $cq_parinf_a.$cq_parinf->bd_fecha <= NOW()) ";
			}
			
			$query .= "WHERE ";
			$query .= "  $cqa.$cq->bd_estado = 1 ";
			
			#region region filtros
			if (!empty($matchup)) {
				$query .= "AND ( ";
				$query .= "  $cqa.$cq->bd_home LIKE :matchup ";
				$query .= "  OR  $cqa.$cq->bd_away LIKE :matchup ";
				$query .= ") ";
			}
			if (!empty($fecha)) {
				$query .= "AND $cqa.$cq->bd_fecha = :$cq->bd_fecha ";
			}
			if (!empty($torneo)) {
				$query .= "AND $cqa.$cq->bd_pais = :torneo ";
			}
			if ($solo_odds_por_revisar == 1) {
				$query .= "AND $cqa.$cq->bd_odds_reviewed = 0 ";
			}
			if ($solo_por_jugar == 1) {
				$query .= "AND $cqa.$cq->bd_hora >= :hora_actual ";
			}
			#endregion filtros
			#region region group order by
			if ($groupby_torneo == 1) {
				$query .= "GROUP BY ";
				$query .= "  $cqa.$cq->bd_id_pais ";
				$query .= "ORDER BY ";
				$query .= "  $cqa.$cq->bd_pais ";
			} else {
				$query .= "ORDER BY ";
				$query .= "  $cqa.$cq->bd_fecha DESC ";
				$query .= "  ,$cqa.$cq->bd_hora ASC ";
				$query .= "  ,$cqa.$cq->bd_id ASC ";
			}
			#endregion order group by
			
			$statement = $conexion->prepare($query);
			
			#region region bindvalues
			if (!empty($matchup)) {
				$statement->bindValue(":matchup", '%' . $matchup . '%');
			}
			if (!empty($fecha)) {
				$statement->bindValue(":$cq->bd_fecha", $fecha);
			}
			if (!empty($torneo)) {
				$statement->bindValue(":torneo", $torneo);
			}
			if ($solo_por_jugar == 1) {
				$statement->bindValue(":hora_actual", $cur_hora);
			}
			#endregion bindvalues
			
			$statement->execute();
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				return array();
			} else {
				$listado = array();
				
				foreach ($resultados as $resultado) {
					$listado[] = self::construct($resultado, $conexion);
				}
				
				return $listado;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_list_solo_odds_por_revisar(PDO $conexion): array
	{
		try {
			$param                          = array();
			$param['solo_odds_por_revisar'] = 1;
			
			return self::getList($param, $conexion);
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function getListNumWeek($conexion): array
	{
		try {
			$curr_year = getYearActual();
			
			$cq     = new self;
			$cqa    = $cq->bd_alias;
			$cq_at  = new ApuestaTipo();
			$cq_ata = $cq_at->bd_alias;
			
			$query = "SELECT ";
			$query .= "  $cqa.$cq->bd_num_week ";
			$query .= "FROM $cq->bd_table $cq->bd_alias ";
			$query .= "WHERE ";
			$query .= "  $cqa.$cq->bd_estado = :$cq->bd_estado ";
			$query .= "GROUP BY ";
			$query .= "  $cqa.$cq->bd_num_week ";
			$query .= "ORDER BY ";
			$query .= "  $cqa.$cq->bd_num_week DESC ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_estado", 1);
			$statement->execute();
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				return array();
			} else {
				$listado = array();
				
				foreach ($resultados as $resultado) {
					$partido           = new Partido;
					$partido->num_week = $resultado[$cq->bd_num_week];
					
					$listado[] = $partido;
				}
				
				return $listado;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_list_numweek_bydaterange($fchrange, $conexion): array
	{
		try {
			$split_fchrange = explode(" - ", $fchrange);
			
			$cq  = new self;
			$cqa = $cq->bd_alias;
			
			$query = "SELECT ";
			$query .= "  $cqa.$cq->bd_num_week ";
			$query .= "FROM $cq->bd_table $cq->bd_alias ";
			$query .= "WHERE ";
			$query .= "  $cqa.$cq->bd_estado = :$cq->bd_estado ";
			$query .= "  AND $cqa.$cq->bd_fecha >= :fchrange1 ";
			$query .= "  AND $cqa.$cq->bd_fecha <= :fchrange2 ";
			$query .= "GROUP BY ";
			$query .= "  $cqa.$cq->bd_num_week ";
			$query .= "ORDER BY ";
			$query .= "  $cqa.$cq->bd_num_week DESC ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_estado", 1);
			$statement->bindValue(":fchrange1", $split_fchrange[0]);
			$statement->bindValue(":fchrange2", $split_fchrange[1]);
			$statement->execute();
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				return array();
			} else {
				$listado = array();
				
				foreach ($resultados as $resultado) {
					$partido           = new Partido;
					$partido->num_week = $resultado[$cq->bd_num_week];
					
					$listado[] = $partido;
				}
				
				return $listado;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function getListFechasPorSemana($numweek, $conexion): array
	{
		try {
			$cq     = new self;
			$cqa    = $cq->bd_alias;
			$cq_at  = new ApuestaTipo();
			$cq_ata = $cq_at->bd_alias;
			
			$query = "SELECT ";
			$query .= "  $cqa.$cq->bd_fecha ";
			$query .= "FROM $cq->bd_table $cq->bd_alias ";
			$query .= "WHERE ";
			$query .= "  $cqa.$cq->bd_estado = :$cq->bd_estado ";
			$query .= "  AND $cqa.$cq->bd_num_week = :$cq->bd_num_week ";
			$query .= "GROUP BY ";
			$query .= "  $cqa.$cq->bd_fecha ";
			$query .= "ORDER BY ";
			$query .= "  $cqa.$cq->bd_fecha DESC ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_estado", 1);
			$statement->bindValue(":$cq->bd_num_week", $numweek);
			$statement->execute();
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				return array();
			} else {
				$listado = array();
				$n       = 0;
				
				foreach ($resultados as $resultado) {
					$listado[$n++] = $resultado[$cq->bd_fecha];
				}
				
				return $listado;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function getListPaises(array $paramref, PDO $conexion): array
	{
		try {
			#region region parametros
			$fecharange = (isset($paramref['fecharange'])) ? $paramref['fecharange'] : "";
			#endregion parametros
			
			if (!empty($fecharange)) {
				$fechas = explode(" - ", $fecharange);
			}
			
			$cq     = new self;
			$cqa    = $cq->bd_alias;
			$cq_at  = new ApuestaTipo();
			$cq_ata = $cq_at->bd_alias;
			
			$query = "SELECT ";
			$query .= "  $cqa.$cq->bd_pais ";
			$query .= "FROM $cq->bd_table $cq->bd_alias ";
			$query .= "WHERE ";
			$query .= "  $cqa.$cq->bd_estado = :$cq->bd_estado ";
			
			if (!empty($fecharange)) {
				$query .= "AND $cqa.$cq->bd_fecha >= :fchrange1 ";
				$query .= "AND $cqa.$cq->bd_fecha <= :fchrange2 ";
			}
			
			$query .= "GROUP BY ";
			$query .= "  $cqa.$cq->bd_pais ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_estado", 1);
			
			if (!empty($fecharange)) {
				$statement->bindValue(":fchrange1", $fechas[0]);
				$statement->bindValue(":fchrange2", $fechas[1]);
			}
			
			$statement->execute();
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				return array();
			} else {
				$listado = array();
				
				foreach ($resultados as $resultado) {
					$partido       = new Partido;
					$partido->pais = $resultado[$cq->bd_pais];
					
					$listado[] = $partido;
				}
				
				return $listado;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_list_torneos($id_partido, $conexion): array
	{
		try {
			$query = <<<SQL
					SELECT
						p.*
					FROM partidos p
					WHERE p.id_partido = :id_partido
					SQL;
			
			$query = "SELECT ";
			$query .= "  $cqa.$cq->bd_fecha ";
			$query .= "FROM $cq->bd_table $cq->bd_alias ";
			$query .= "WHERE ";
			$query .= "  $cqa.$cq->bd_estado = :$cq->bd_estado ";
			$query .= "  AND $cqa.$cq->bd_num_week = :$cq->bd_num_week ";
			$query .= "GROUP BY ";
			$query .= "  $cqa.$cq->bd_fecha ";
			$query .= "ORDER BY ";
			$query .= "  $cqa.$cq->bd_fecha DESC ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_estado", 1);
			$statement->bindValue(":$cq->bd_num_week", $numweek);
			$statement->execute();
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				return array();
			} else {
				$listado = array();
				$n       = 0;
				
				foreach ($resultados as $resultado) {
					$listado[$n++] = $resultado[$cq->bd_fecha];
				}
				
				return $listado;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	function add($conexion): void
	{
		try {
			$this->validateData($conexion);
			
			$cq = new self;
			
			$query = "INSERT INTO $cq->bd_table (";
			$query .= "  $cq->bd_home ";
			$query .= "  ,$cq->bd_away ";
			$query .= "  ,$cq->bd_pais ";
			$query .= "  ,$cq->bd_id_pais ";
			$query .= "  ,$cq->bd_diasemana ";
			$query .= "  ,$cq->bd_fecha ";
			$query .= "  ,$cq->bd_hora ";
			$query .= "  ,$cq->bd_num_week ";
			$query .= "  ,$cq->bd_formhome ";
			$query .= "  ,$cq->bd_formaway ";
			$query .= "  ,$cq->bd_horamilitar ";
			$query .= "  ,$cq->bd_home_xg ";
			$query .= "  ,$cq->bd_away_xg ";
			$query .= ") VALUES (";
			$query .= "  :$cq->bd_home ";
			$query .= "  ,:$cq->bd_away ";
			$query .= "  ,:$cq->bd_pais ";
			$query .= "  ,:$cq->bd_id_pais ";
			$query .= "  ,:$cq->bd_diasemana ";
			$query .= "  ,:$cq->bd_fecha ";
			$query .= "  ,:$cq->bd_hora ";
			$query .= "  ,:$cq->bd_num_week ";
			$query .= "  ,:$cq->bd_formhome ";
			$query .= "  ,:$cq->bd_formaway ";
			$query .= "  ,:$cq->bd_horamilitar ";
			$query .= "  ,:$cq->bd_home_xg ";
			$query .= "  ,:$cq->bd_away_xg ";
			$query .= ") ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_home", $this->home);
			$statement->bindValue(":$cq->bd_away", $this->away);
			$statement->bindValue(":$cq->bd_pais", $this->pais);
			$statement->bindValue(":$cq->bd_id_pais", ordena($this->pais_torneo->id));
			$statement->bindValue(":$cq->bd_diasemana", $this->diasemana);
			$statement->bindValue(":$cq->bd_fecha", $this->fecha);
			$statement->bindValue(":$cq->bd_hora", $this->hora);
			$statement->bindValue(":$cq->bd_num_week", $this->num_week);
			$statement->bindValue(":$cq->bd_formhome", $this->formhome);
			$statement->bindValue(":$cq->bd_formaway", $this->formaway);
			$statement->bindValue(":$cq->bd_horamilitar", $this->horamilitar);
			$statement->bindValue(":$cq->bd_home_xg", $this->home_xg);
			$statement->bindValue(":$cq->bd_away_xg", $this->away_xg);
			$statement->execute();
			
			$this->id = desordena($conexion->lastInsertId());
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	function modify($conexion): void
	{
		try {
			$this->validateData($conexion);
			
			$cq = new self;
			
			$query = " UPDATE $cq->bd_table SET ";
			$query .= "  $cq->bd_home = :$cq->bd_home ";
			$query .= "  ,$cq->bd_away = :$cq->bd_away ";
			$query .= "  ,$cq->bd_pais = :$cq->bd_pais ";
			$query .= "  ,$cq->bd_diasemana = :$cq->bd_diasemana ";
			$query .= "  ,$cq->bd_fecha = :$cq->bd_fecha ";
			$query .= "  ,$cq->bd_hora = :$cq->bd_hora ";
			$query .= "  ,$cq->bd_num_week = :$cq->bd_num_week ";
			$query .= "WHERE ";
			$query .= "  $cq->bd_id = :$cq->bd_id ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_home", $this->home);
			$statement->bindValue(":$cq->bd_away", $this->away);
			$statement->bindValue(":$cq->bd_pais", $this->pais);
			$statement->bindValue(":$cq->bd_diasemana", $this->diasemana);
			$statement->bindValue(":$cq->bd_fecha", $this->fecha);
			$statement->bindValue(":$cq->bd_hora", $this->hora);
			$statement->bindValue(":$cq->bd_num_week", $this->num_week);
			$statement->bindValue(":$cq->bd_id", ordena($this->id));
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function modify_marcar_odds_reviewed($idpartido, PDO $conexion): void
	{
		try {
			$cq = new self;
			
			$query = " UPDATE $cq->bd_table SET ";
			$query .= "  $cq->bd_odds_reviewed = 1 ";
			$query .= "WHERE ";
			$query .= "  $cq->bd_id = :$cq->bd_id ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_id", ordena($idpartido));
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function modify_marcar_revisado_probabilidades($id_partido, PDO $conexion): void
	{
		try {
			$query = <<<SQL
					UPDATE partidos SET
						revisado_probabilidades = 1
					WHERE
						id_partido = :id_partido
					SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":id_partido", ordena($id_partido));
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function delete($id, $conexion): void
	{
		try {
			$cq = new self;
			
			$query = " UPDATE $cq->bd_table SET ";
			$query .= "  $cq->bd_estado = :$cq->bd_estado ";
			$query .= "WHERE ";
			$query .= "  $cq->bd_id = :$cq->bd_id ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_estado", 0);
			$statement->bindValue(":$cq->bd_id", ordena($id));
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function delete_hasta_hora($fecha, $hora, $conexion): void
	{
		try {
			$cq = new self;
			
			$query = " UPDATE $cq->bd_table SET ";
			$query .= "  $cq->bd_estado = 0 ";
			$query .= "WHERE ";
			$query .= "  $cq->bd_fecha = :$cq->bd_fecha ";
			$query .= "  AND $cq->bd_hora < :$cq->bd_hora ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_fecha", $fecha);
			$statement->bindValue(":$cq->bd_hora", $hora);
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	public static function addCorners($idpartido, $archivocsv, $conexion)
	{
		try {
			$n = 0;
			
			foreach ($archivocsv as $filacsv) {
				$filacsv   = mb_convert_encoding($filacsv, "UTF-8", mb_detect_encoding($filacsv, "UTF-8, ISO-8859-1, ISO-8859-15", true));
				$separador = ',';
				
				if ($n > 0) {
					$campos      = explode($separador, $filacsv);
					$fecha       = strtoupper(trim($campos[0]));
					$torneo      = strtoupper(trim($campos[1]));
					$home        = strtoupper(trim($campos[3]));
					$homecorners = trim($campos[4]);
					$away        = strtoupper(trim($campos[7]));
					$awaycorners = trim($campos[6]);
					
					$omitir = 0;
					
					if ($torneo == 'FRIEND') {
						$omitir = 1;
					}
					if ($homecorners == '?' || $awaycorners == '?') {
						$omitir = 1;
					}
					
					if ($omitir == 0) {
						$partidocorner              = new PartidoCorner;
						$partidocorner->idpartido   = $idpartido;
						$partidocorner->home        = $home;
						$partidocorner->homecorners = $homecorners;
						$partidocorner->away        = $away;
						$partidocorner->awaycorners = $awaycorners;
						$partidocorner->torneo      = $torneo;
						$partidocorner->fecha       = $fecha;
						$partidocorner->add($conexion);
					}
				}
				
				$n++;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function definirBgColorForm($form): string
	{
		try {
			if ($form >= 2) {
				return 'bg-green';
			} elseif ($form >= 1.51 && $form < 2) {
				return 'bg-green-300';
			} elseif ($form >= 1.1 && $form < 1.51) {
				return 'bg-yellow-800';
			} elseif ($form < 1.1) {
				return 'bg-red-400';
			} else {
				return 'bg-gray';
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	private function validateData($conexion): void
	{
		try {
			validar_textovacio($this->home, 'Debe especificar el Home');
			validar_textovacio($this->away, 'Debe especificar el Away');
			validar_textovacio($this->pais, 'Debe especificar el Pais');
			validar_textovacio($this->fecha, 'Debe especificar la fecha');
			
			$this->home      = trim($this->home);
			$this->away      = trim($this->away);
			$this->pais      = trim($this->pais);
			$this->diasemana = formatDateDayofWeek($this->fecha);
			$this->num_week  = formatDateNumWeek($this->fecha);
			
			//verificar si el pais existe
			$idpais = Pais::getByNombre($this->pais, $conexion);
			
			if (empty($idpais)) {
				$new_pais                 = new Pais();
				$new_pais->nombre         = $this->pais;
				$new_pais->perc_penalidad = 0;
				$new_pais->add($conexion);
				
				$this->pais_torneo->id = $new_pais->id;
			} else {
				$this->pais_torneo->id = $idpais;
			}
			
			if (self::getExiste($this, $conexion)) {
				throw new Exception('Este partido ya existe');
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function sortArrayByWinRatioPorcProfit($partidos): array
	{
		try {
			usort($partidos, function ($a, $b) {
				return [$b->reporte_winratioporpais['porc_profit'], $b->reporte_winratioporpais['winratio']] <=> [$a->reporte_winratioporpais['porc_profit'], $a->reporte_winratioporpais['winratio']];
			});
			
			return $partidos;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function formatFechaDiasHora($fecha, $horastring): string
	{
		try {
			if (!empty($fecha)) {
				$fecha_actual = create_date();
				$dias         = getDateDiffDaysNonLiteral($fecha, $fecha_actual);
				
				if ($dias > 0) {
					$fechadiashora = 'FT | ' . $horastring;
					
				} elseif ($dias == 0) {
					$fechadiashora = 'Hoy | ' . $horastring;
					
				} else {
					$fechadiashora = '+' . abs($dias) . ' | ' . $horastring;
				}
				
				return $fechadiashora;
				
			} else {
				return '';
			}
			
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
}

?>